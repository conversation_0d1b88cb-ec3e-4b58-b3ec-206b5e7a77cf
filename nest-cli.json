{"collection": "@nestjs/schematics", "sourceRoot": "apps/api-gateway/src", "monorepo": true, "root": "apps/api-gateway", "compilerOptions": {"webpack": true, "tsConfigPath": "apps/api-gateway/tsconfig.app.json"}, "projects": {"api-gateway": {"type": "application", "root": "apps/api-gateway", "entryFile": "main", "sourceRoot": "apps/api-gateway/src", "compilerOptions": {"tsConfigPath": "apps/api-gateway/tsconfig.app.json"}}, "api-gateway-azure": {"type": "application", "root": "apps/api-gateway", "entryFile": "azure-function", "sourceRoot": "apps/api-gateway/src", "compilerOptions": {"tsConfigPath": "apps/api-gateway/tsconfig.app.json"}}, "user-service": {"type": "application", "root": "apps/user-service", "entryFile": "main", "sourceRoot": "apps/user-service/src", "compilerOptions": {"tsConfigPath": "apps/user-service/tsconfig.app.json"}}}}