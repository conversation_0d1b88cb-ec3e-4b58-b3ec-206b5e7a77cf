{"name": "nest-microservices", "version": "0.0.1", "private": true, "workspaces": ["apps/*"], "scripts": {"build": "nest build", "start:dev": "nest start --watch", "start:prod": "node dist/main", "test": "jest"}, "dependencies": {"@azure/functions": "^3.5.0", "@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/microservices": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "ts-loader": "^9.5.2"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.1.3"}}