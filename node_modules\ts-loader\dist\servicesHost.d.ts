import type * as typescript from 'typescript';
import type * as webpack from 'webpack';
import type { FilePathKey, LoaderOptions, ServiceHostWhichMayBeCacheable, SolutionBuilderWithWatchHost, TSInstance, WatchHost } from './interfaces';
/**
 * Create the TypeScript language service
 */
export declare function makeServicesHost(scriptRegex: RegExp, loader: webpack.LoaderContext<LoaderOptions>, instance: TSInstance, projectReferences?: ReadonlyArray<typescript.ProjectReference>): ServiceHostWhichMayBeCacheable;
export declare function updateFileWithText(instance: TSInstance, key: FilePathKey, filePath: string, text: (nFilePath: string) => string): void;
/**
 * Create the TypeScript Watch host
 */
export declare function makeWatchHost(scriptRegex: RegExp, loader: webpack.LoaderContext<LoaderOptions>, instance: TSInstance, projectReferences?: ReadonlyArray<typescript.ProjectReference>): WatchHost;
/**
 * Create the TypeScript Watch host
 */
export declare function makeSolutionBuilderHost(scriptRegex: RegExp, loader: webpack.LoaderContext<LoaderOptions>, instance: TSInstance): SolutionBuilderWithWatchHost;
export declare function getSolutionErrors(instance: TSInstance, context: string): webpack.WebpackError[];
//# sourceMappingURL=servicesHost.d.ts.map