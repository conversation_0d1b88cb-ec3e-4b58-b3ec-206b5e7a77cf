const { ClientProxyFactory, Transport } = require('@nestjs/microservices');
const url = require('url');

let userServiceClient;

function createUserServiceClient() {
  if (!userServiceClient) {
    userServiceClient = ClientProxyFactory.create({
      transport: Transport.TCP,
      options: {
        host: 'localhost',
        port: 3001,
      },
    });
  }
  return userServiceClient;
}

async function handleRequest(req) {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;
  const method = req.method;
  const query = parsedUrl.query;

  // Handle different routes
  if (pathname === '/api' && method === 'GET') {
    return handleSwaggerUI();
  } else if (pathname === '/api-docs' && method === 'GET') {
    return handleSwaggerDocs();
  } else if (pathname === '/users' && method === 'GET') {
    return await handleGetUsers();
  } else if (pathname === '/users' && method === 'POST') {
    return await handleCreateUser(req.body);
  } else if (pathname === '/' && method === 'GET') {
    return handleRoot();
  } else {
    return {
      status: 404,
      headers: { 'content-type': 'application/json' },
      body: JSON.stringify({ error: 'Not found' })
    };
  }
}

function handleSwaggerUI() {
  const swaggerHtml = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>API Gateway - Swagger UI</title>
      <link rel="icon" type="image/png" href="https://petstore.swagger.io/favicon-32x32.png" sizes="32x32" />
      <link rel="icon" type="image/png" href="https://petstore.swagger.io/favicon-16x16.png" sizes="16x16" />
      <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css" />
      <style>
        html {
          box-sizing: border-box;
          overflow: -moz-scrollbars-vertical;
          overflow-y: scroll;
        }
        *, *:before, *:after {
          box-sizing: inherit;
        }
        body {
          margin:0;
          background: #fafafa;
        }
        .swagger-ui .topbar {
          background-color: #89bf04;
          padding: 8px 0;
        }
        .swagger-ui .topbar .download-url-wrapper {
          display: none;
        }
        .swagger-ui .topbar .link {
          content: "API Gateway";
        }
        .swagger-ui .topbar .link img {
          display: none;
        }
        .swagger-ui .topbar .link:after {
          content: "API Gateway";
          color: white;
          font-weight: bold;
          font-size: 1.5em;
        }
      </style>
    </head>
    <body>
      <div id="swagger-ui"></div>
      <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js" charset="UTF-8"></script>
      <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-standalone-preset.js" charset="UTF-8"></script>
      <script>
        window.onload = function() {
          const ui = SwaggerUIBundle({
            url: '/api-docs',
            dom_id: '#swagger-ui',
            deepLinking: true,
            presets: [
              SwaggerUIBundle.presets.apis,
              SwaggerUIStandalonePreset
            ],
            plugins: [
              SwaggerUIBundle.plugins.DownloadUrl
            ],
            layout: "StandaloneLayout",
            validatorUrl: "https://validator.swagger.io/validator",
            docExpansion: "list",
            operationsSorter: "alpha"
          });
        };
      </script>
    </body>
    </html>
  `;

  return {
    status: 200,
    headers: { 'content-type': 'text/html' },
    body: swaggerHtml
  };
}

function handleSwaggerDocs() {
  const swaggerDoc = {
    openapi: '3.0.0',
    info: {
      title: 'API Gateway',
      description: 'API Gateway for microservices',
      version: '1.0.0'
    },
    paths: {
      '/users': {
        get: {
          summary: 'Get all users',
          responses: {
            '200': {
              description: 'Return all users'
            }
          }
        },
        post: {
          summary: 'Create a new user',
          requestBody: {
            required: true,
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    name: { type: 'string', example: 'John Doe' },
                    email: { type: 'string', example: '<EMAIL>' }
                  }
                }
              }
            }
          },
          responses: {
            '201': {
              description: 'User created successfully'
            }
          }
        }
      }
    }
  };

  return {
    status: 200,
    headers: { 'content-type': 'application/json' },
    body: JSON.stringify(swaggerDoc)
  };
}

async function handleGetUsers() {
  try {
    const client = createUserServiceClient();
    const result = await client.send({ cmd: 'get_users' }, {}).toPromise();
    return {
      status: 200,
      headers: { 'content-type': 'application/json' },
      body: JSON.stringify(result)
    };
  } catch (error) {
    return {
      status: 500,
      headers: { 'content-type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to get users', message: error.message })
    };
  }
}

async function handleCreateUser(body) {
  try {
    const client = createUserServiceClient();
    const result = await client.send({ cmd: 'create_user' }, body).toPromise();
    return {
      status: 201,
      headers: { 'content-type': 'application/json' },
      body: JSON.stringify(result)
    };
  } catch (error) {
    return {
      status: 500,
      headers: { 'content-type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to create user', message: error.message })
    };
  }
}

function handleRoot() {
  return {
    status: 200,
    headers: { 'content-type': 'application/json' },
    body: JSON.stringify({
      message: 'API Gateway is running',
      endpoints: {
        swagger: '/api',
        users: '/users'
      }
    })
  };
}

module.exports = async function (context, req) {
  try {
    context.log('HTTP trigger function processed a request.');

    // Handle the request using our simple router
    const result = await handleRequest(req);

    context.res = {
      status: result.status,
      headers: result.headers,
      body: result.body
    };

  } catch (error) {
    context.log.error('Error processing request:', error);
    context.res = {
      status: 500,
      headers: { 'content-type': 'application/json' },
      body: JSON.stringify({ error: 'Internal server error', message: error.message })
    };
  }
};
