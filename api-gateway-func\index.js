// Import reflect-metadata first
require('reflect-metadata');

const { createServer } = require('http');
const { parse } = require('url');

let cachedApp;

async function bootstrap() {
  if (!cachedApp) {
    // Import NestJS modules dynamically to avoid webpack issues
    const { NestFactory } = require('@nestjs/core');
    const { SwaggerModule, DocumentBuilder } = require('@nestjs/swagger');

    // Import the AppModule from the compiled bundle
    const { AppModule } = require('../dist/apps/api-gateway/main.js');

    const app = await NestFactory.create(AppModule);

    const config = new DocumentBuilder()
      .setTitle('API Gateway')
      .setDescription('API Gateway for microservices')
      .setVersion('1.0')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api', app, document);

    await app.init();
    cachedApp = app;
  }

  return cachedApp;
}

module.exports = async function (context, req) {
  try {
    context.log('HTTP trigger function processed a request.');

    const app = await bootstrap();
    const expressInstance = app.getHttpAdapter().getInstance();

    // Create a proper HTTP request/response cycle
    await new Promise((resolve, reject) => {
      const mockReq = {
        ...req,
        method: req.method,
        url: req.url,
        headers: req.headers || {},
        body: req.body,
        query: req.query || {}
      };

      const mockRes = {
        statusCode: 200,
        headers: {},
        end: (chunk) => {
          context.res = {
            status: mockRes.statusCode,
            headers: mockRes.headers,
            body: chunk
          };
          resolve();
        },
        setHeader: (key, value) => {
          mockRes.headers[key] = value;
        },
        writeHead: (status, headers) => {
          mockRes.statusCode = status;
          if (headers) {
            Object.assign(mockRes.headers, headers);
          }
        },
        write: () => {},
        on: () => {},
        once: () => {}
      };

      // Handle the request through Express
      expressInstance(mockReq, mockRes);
    });

  } catch (error) {
    context.log.error('Error processing request:', error);
    context.res = {
      status: 500,
      body: { error: 'Internal server error', message: error.message }
    };
  }
};
