const { Context, HttpRequest } = require('@azure/functions');
const { createServer } = require('http');
const { parse } = require('url');
const { NestFactory } = require('@nestjs/core');
const { SwaggerModule, DocumentBuilder } = require('@nestjs/swagger');

// Import the compiled AppModule
const { AppModule } = require('../dist/apps/api-gateway/main.js');

let cachedServer;

async function bootstrap() {
  if (!cachedServer) {
    const app = await NestFactory.create(AppModule);
    
    const config = new DocumentBuilder()
      .setTitle('API Gateway')
      .setDescription('API Gateway for microservices')
      .setVersion('1.0')
      .build();
    
    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api', app, document);
    
    await app.init();
    
    const expressInstance = app.getHttpAdapter().getInstance();
    cachedServer = createServer(expressInstance);
  }
  
  return cachedServer;
}

module.exports = async function (context, req) {
  const server = await bootstrap();
  
  context.log('HTTP trigger function processed a request.');
  
  const { pathname, search } = parse(req.url || '/', true);
  
  await new Promise((resolve) => {
    context.res = {
      status: 200,
      headers: {},
      body: [],
    };
    
    const mockRes = {
      end: (chunk) => {
        context.res.body = chunk;
        resolve();
      },
      getHeader: () => {},
      setHeader: (key, value) => {
        context.res.headers[key] = value;
      },
      writeHead: (status) => {
        context.res.status = status;
      },
    };
    
    server.emit('request', req, mockRes);
  });
};
