const express = require('express');
const { ClientProxy, ClientProxyFactory, Transport } = require('@nestjs/microservices');

let app;
let userServiceClient;

function createUserServiceClient() {
  if (!userServiceClient) {
    userServiceClient = ClientProxyFactory.create({
      transport: Transport.TCP,
      options: {
        host: 'localhost',
        port: 3001,
      },
    });
  }
  return userServiceClient;
}

function createExpressApp() {
  if (!app) {
    app = express();
    app.use(express.json());

    const client = createUserServiceClient();

    // Swagger documentation endpoint
    app.get('/api', (req, res) => {
      const swaggerDoc = {
        openapi: '3.0.0',
        info: {
          title: 'API Gateway',
          description: 'API Gateway for microservices',
          version: '1.0.0'
        },
        paths: {
          '/users': {
            get: {
              summary: 'Get all users',
              responses: {
                '200': {
                  description: 'Return all users',
                  content: {
                    'application/json': {
                      schema: {
                        type: 'array',
                        items: {
                          type: 'object',
                          properties: {
                            id: { type: 'number' },
                            name: { type: 'string' },
                            email: { type: 'string' }
                          }
                        }
                      }
                    }
                  }
                }
              }
            },
            post: {
              summary: 'Create a new user',
              requestBody: {
                required: true,
                content: {
                  'application/json': {
                    schema: {
                      type: 'object',
                      properties: {
                        name: { type: 'string', example: 'John Doe' },
                        email: { type: 'string', example: '<EMAIL>' }
                      },
                      required: ['name', 'email']
                    }
                  }
                }
              },
              responses: {
                '201': {
                  description: 'User created successfully',
                  content: {
                    'application/json': {
                      schema: {
                        type: 'object',
                        properties: {
                          id: { type: 'number' },
                          name: { type: 'string' },
                          email: { type: 'string' }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      };

      // Return Swagger UI HTML
      const swaggerHtml = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>API Gateway - Swagger UI</title>
          <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui.css" />
        </head>
        <body>
          <div id="swagger-ui"></div>
          <script src="https://unpkg.com/swagger-ui-dist@3.52.5/swagger-ui-bundle.js"></script>
          <script>
            SwaggerUIBundle({
              url: '/api-docs',
              dom_id: '#swagger-ui',
              presets: [
                SwaggerUIBundle.presets.apis,
                SwaggerUIBundle.presets.standalone
              ]
            });
          </script>
        </body>
        </html>
      `;
      res.setHeader('Content-Type', 'text/html');
      res.send(swaggerHtml);
    });

    // Swagger JSON endpoint
    app.get('/api-docs', (req, res) => {
      const swaggerDoc = {
        openapi: '3.0.0',
        info: {
          title: 'API Gateway',
          description: 'API Gateway for microservices',
          version: '1.0.0'
        },
        paths: {
          '/users': {
            get: {
              summary: 'Get all users',
              responses: {
                '200': {
                  description: 'Return all users'
                }
              }
            },
            post: {
              summary: 'Create a new user',
              requestBody: {
                required: true,
                content: {
                  'application/json': {
                    schema: {
                      type: 'object',
                      properties: {
                        name: { type: 'string', example: 'John Doe' },
                        email: { type: 'string', example: '<EMAIL>' }
                      }
                    }
                  }
                }
              },
              responses: {
                '201': {
                  description: 'User created successfully'
                }
              }
            }
          }
        }
      };
      res.json(swaggerDoc);
    });

    // Users endpoints
    app.get('/users', async (req, res) => {
      try {
        const result = await client.send({ cmd: 'get_users' }, {}).toPromise();
        res.json(result);
      } catch (error) {
        res.status(500).json({ error: 'Failed to get users', message: error.message });
      }
    });

    app.post('/users', async (req, res) => {
      try {
        const result = await client.send({ cmd: 'create_user' }, req.body).toPromise();
        res.status(201).json(result);
      } catch (error) {
        res.status(500).json({ error: 'Failed to create user', message: error.message });
      }
    });

    // Root endpoint
    app.get('/', (req, res) => {
      res.json({
        message: 'API Gateway is running',
        endpoints: {
          swagger: '/api',
          users: '/users'
        }
      });
    });
  }

  return app;
}

module.exports = async function (context, req) {
  try {
    context.log('HTTP trigger function processed a request.');

    const expressApp = createExpressApp();

    // Create a proper HTTP request/response cycle
    await new Promise((resolve, reject) => {
      const mockReq = {
        method: req.method || 'GET',
        url: req.url || '/',
        headers: req.headers || {},
        body: req.body,
        query: req.query || {},
        params: req.params || {},
        rawBody: req.rawBody,
        originalUrl: req.url || '/',
        path: req.url ? req.url.split('?')[0] : '/',
        protocol: 'https',
        secure: true,
        ip: '127.0.0.1',
        ips: [],
        subdomains: [],
        connection: {},
        socket: {},
        get: function(name) {
          return this.headers[name.toLowerCase()];
        }
      };

      const mockRes = {
        statusCode: 200,
        headers: {},
        headersSent: false,
        locals: {},
        end: function(chunk) {
          context.res = {
            status: this.statusCode,
            headers: this.headers,
            body: chunk
          };
          resolve();
        },
        setHeader: function(key, value) {
          this.headers[key.toLowerCase()] = value;
        },
        getHeader: function(key) {
          return this.headers[key.toLowerCase()];
        },
        removeHeader: function(key) {
          delete this.headers[key.toLowerCase()];
        },
        writeHead: function(status, headers) {
          this.statusCode = status;
          if (headers) {
            Object.assign(this.headers, headers);
          }
        },
        write: function() {},
        status: function(code) {
          this.statusCode = code;
          return this;
        },
        json: function(obj) {
          this.setHeader('content-type', 'application/json');
          this.end(JSON.stringify(obj));
        },
        send: function(data) {
          this.end(data);
        },
        redirect: function(url) {
          this.statusCode = 302;
          this.setHeader('location', url);
          this.end();
        },
        on: function() {},
        once: function() {},
        emit: function() {}
      };

      // Handle the request through Express
      expressApp(mockReq, mockRes);
    });

  } catch (error) {
    context.log.error('Error processing request:', error);
    context.res = {
      status: 500,
      headers: { 'content-type': 'application/json' },
      body: JSON.stringify({ error: 'Internal server error', message: error.message })
    };
  }
};
