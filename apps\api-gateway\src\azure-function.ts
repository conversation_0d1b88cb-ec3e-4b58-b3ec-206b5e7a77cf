import { Context, HttpRequest } from '@azure/functions';
import { createServer } from 'http';
import { parse } from 'url';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';

let cachedServer: any;

async function bootstrap() {
  if (!cachedServer) {
    const app = await NestFactory.create(AppModule);
    
    const config = new DocumentBuilder()
      .setTitle('API Gateway')
      .setDescription('API Gateway for microservices')
      .setVersion('1.0')
      .build();
    
    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api', app, document);
    
    await app.init();
    
    const expressInstance = app.getHttpAdapter().getInstance();
    cachedServer = createServer(expressInstance);
  }
  
  return cachedServer;
}

export default async function (context: Context, req: HttpRequest): Promise<void> {
  const server = await bootstrap();
  
  context.log('HTTP trigger function processed a request.');
  
  const { pathname, search } = parse(req.url || '/', true);
  
  await new Promise<void>((resolve) => {
    context.res = {
      status: 200,
      headers: {},
      body: [],
    };
    
    const mockRes = {
      end: (chunk) => {
        context.res.body = chunk;
        resolve();
      },
      getHeader: () => {},
      setHeader: (key, value) => {
        context.res.headers[key] = value;
      },
      writeHead: (status) => {
        context.res.status = status;
      },
    };
    
    server.emit('request', req, mockRes);
  });
}